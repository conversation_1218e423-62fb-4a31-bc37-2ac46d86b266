/**
 * Environment setup for tests
 * Loads environment variables from .env file for tests
 */

const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from .env file
const envPath = path.resolve(__dirname, '..', '.env');
dotenv.config({ path: envPath });

// Override specific settings for test environment
process.env.NODE_ENV = 'test';
process.env.ENVIRONMENT = 'test';

// Disable AWS SDK retries for faster tests
process.env.AWS_MAX_ATTEMPTS = '1';
